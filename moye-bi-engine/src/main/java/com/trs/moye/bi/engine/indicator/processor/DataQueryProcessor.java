package com.trs.moye.bi.engine.indicator.processor;

import com.trs.moye.base.common.response.PageResponse;
import com.trs.moye.base.common.utils.DateTimeUtils;
import com.trs.moye.base.data.indicator.dao.IndicatorPeriodConfigMapper;
import com.trs.moye.base.data.indicator.entity.DataModelIndicatorField;
import com.trs.moye.base.data.indicator.entity.IndicatorApplicationFieldConfig;
import com.trs.moye.base.data.indicator.entity.IndicatorPeriodConfig;
import com.trs.moye.base.data.indicator.entity.IndicatorPeriodConfigEntity;
import com.trs.moye.base.data.indicator.entity.IndicatorSortField;
import com.trs.moye.base.data.indicator.entity.StatisticPeriod;
import com.trs.moye.base.data.indicator.enums.DataCompletionStrategy;
import com.trs.moye.base.data.indicator.enums.IndicatorPeriodType;
import com.trs.moye.base.data.indicator.enums.IndicatorSortOrder;
import com.trs.moye.base.data.indicator.utils.IndicatorPeriodConverter;
import com.trs.moye.bi.engine.common.request.CodeSearchParams;
import com.trs.moye.bi.engine.feign.SearchFeign;
import com.trs.moye.bi.engine.indicator.CalculationFunction;
import com.trs.moye.bi.engine.indicator.DataCompletionService;
import com.trs.moye.bi.engine.indicator.config.IndicatorQueryProperties;
import com.trs.moye.bi.engine.indicator.context.IndicatorDataContext;
import com.trs.moye.bi.engine.indicator.context.QueryContext;
import com.trs.moye.bi.engine.indicator.exception.IndicatorQueryException;
import com.trs.moye.bi.engine.indicator.util.IndicatorDataSorter;
import com.trs.moye.bi.engine.indicator.util.IndicatorQueryBuilder;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.function.Function;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

/**
 * 数据查询处理器.
 * <p>
 * 负责协调整个指标查询和计算流程
 *
 * <AUTHOR>
 * @since 2025/07/23
 */
@Slf4j
@Component
public class DataQueryProcessor {

    /**
     * 搜索服务.
     */
    @Resource
    private SearchFeign searchFeign;

    /**
     * 数据补齐服务.
     */
    @Resource
    private DataCompletionService dataCompletionService;

    /**
     * 指标查询执行器.
     */
    @Resource
    private ExecutorService indicatorQueryExecutor;

    /**
     * 指标查询配置属性.
     */
    @Resource
    private IndicatorQueryProperties indicatorQueryProperties;

    /**
     * 指标周期配置数据访问层.
     */
    @Resource
    private IndicatorPeriodConfigMapper indicatorPeriodConfigMapper;

    /**
     * 处理数据查询的主要流程.
     *
     * @param context 查询上下文
     * @return 处理后的分页结果
     */
    public PageResponse<Map<String, Object>> processDataQuery(final QueryContext context) {
        log.info("开始处理数据查询, dataModelId: {}, tableName: {}",
            context.getDataModelId(), context.getTableName());

        final long startTime = System.currentTimeMillis();

        try {
            // 1. 执行当前周期数据查询
            List<Map<String, Object>> currentData = executeCurrentDataQuery(context);

            // 2. 初始化数据上下文
            initializeDataContext(currentData, context);

            // 3. 并行获取历史数据
            if (context.needsHistoricalData()) {
                executeHistoricalDataQuery(context);
            }

            // 4. 获取当前周期数据
            List<Map<String, Object>> resultData = getCurrentPeriodData();
            if (resultData.isEmpty()) {
                log.info("当前周期数据为空, 返回空结果");
                return new PageResponse<>();
            }

            // 5. 数据补齐处理
            if (context.needsDataCompletion()) {
                resultData = performDataCompletion(resultData, context);
            }

            if (resultData.isEmpty()) {
                log.info("数据补齐后结果为空, 返回空结果");
                return new PageResponse<>();
            }

            // 6. 执行指标计算
            Map<String, Map<String, Object>> calculatedValues = performCalculations(context);

            // 7. 合并计算结果
            mergeCalculationResults(resultData, calculatedValues);

            // 8. 处理排序
            resultData = applySorting(resultData, context);

            // 9. 按目标周期筛选
            resultData = filterByTargetPeriods(resultData, context);

            // 10. 过滤返回字段
            resultData = filterReturnFields(resultData, context.getRequest().getReturnFields());

            final long duration = System.currentTimeMillis() - startTime;
            log.info("数据查询处理完成, dataModelId: {}, 结果数量: {}, 耗时: {}ms",
                context.getDataModelId(), resultData.size(), duration);

            return new PageResponse<>(resultData, resultData.size());

        } catch (IndicatorQueryException e) {
            log.error("指标查询处理失败: {}", e.getMessage(), e);
            throw e;
        } catch (Exception e) {
            log.error("数据查询处理发生未知错误, dataModelId: {}", context.getDataModelId(), e);
            throw IndicatorQueryException.dataProcessingError("数据查询处理", e);
        } finally {
            // 确保清理上下文
            IndicatorDataContext.clear();
            final long totalTime = System.currentTimeMillis() - startTime;
            log.debug("数据查询处理总耗时: {}ms", totalTime);
        }
    }

    /**
     * 执行当前周期数据查询
     *
     * @param context 上下文
     * @return {@link List }<{@link Map }<{@link String }, {@link Object }>>
     */
    private List<Map<String, Object>> executeCurrentDataQuery(QueryContext context) {
        try {
            log.debug("执行当前周期数据查询, dataModelId: {}", context.getDataModelId());

            IndicatorQueryBuilder queryBuilder = new IndicatorQueryBuilder(
                context.getRequest(), context.getFieldGroups(), context.getDataConnection());
            String sql = queryBuilder.buildConditionalQuerySql();

            List<Map<String, Object>> result = executeQuery(context.getConnectionId(), sql);

            log.debug("当前周期数据查询完成, 结果数量: {}", result.size());
            return result;

        } catch (Exception e) {
            log.error("当前周期数据查询失败, dataModelId: {}", context.getDataModelId(), e);
            throw IndicatorQueryException.dataQueryError("当前周期数据查询", e);
        }
    }

    /**
     * 初始化数据上下文
     *
     * @param currentData 当前数据
     * @param context     上下文
     */
    private void initializeDataContext(List<Map<String, Object>> currentData, QueryContext context) {
        try {
            log.debug("初始化数据上下文, dataModelId: {}", context.getDataModelId());

            List<String> dimsFields = context.getStatisticDimensions();
            String timeField = context.getFieldGroups().startTimeField();

            Function<Map<String, Object>, String> dimKeyGenerator = row -> dimsFields.stream()
                .map(f -> String.valueOf(row.get(f)))
                .collect(Collectors.joining("_"));

            Function<Map<String, Object>, String> keyGenerator = row ->
                dimKeyGenerator.apply(row) + "_" + row.get(timeField).toString();

            List<Map<String, Object>> baseData = new ArrayList<>(currentData);
            IndicatorDataContext.setContext(new IndicatorDataContext.DataContext(
                new IndicatorDataContext.BaseData(baseData),
                currentData,
                timeField,
                dimsFields,
                context.getRequest().getStatisticPeriod(),
                context.getPeriodConfig() != null ? context.getPeriodConfig().getPeriodType() : null,
                keyGenerator,
                dimKeyGenerator,
                searchFeign,
                context.getConnectionId()
            ));

            log.debug("数据上下文初始化完成");

        } catch (Exception e) {
            log.error("初始化数据上下文失败, dataModelId: {}", context.getDataModelId(), e);
            throw IndicatorQueryException.dataProcessingError("初始化数据上下文", e);
        }
    }

    /**
     * 执行SQL查询
     *
     * @param connectionId 连接id
     * @param sql          SQL
     * @return {@link List }<{@link Map }<{@link String }, {@link Object }>>
     */
    private List<Map<String, Object>> executeQuery(Integer connectionId, String sql) {
        try {
            CodeSearchParams codeSearchParams = new CodeSearchParams();
            codeSearchParams.setCode(sql);
            return searchFeign.indicatorQuery(connectionId, codeSearchParams);
        } catch (Exception e) {
            log.error("SQL查询执行失败, connectionId: {}, sql: {}", connectionId, sql, e);
            throw IndicatorQueryException.dataQueryError(sql, e);
        }
    }

    /**
     * 获取当前周期数据
     *
     * @return {@link List }<{@link Map }<{@link String }, {@link Object }>>
     */
    private List<Map<String, Object>> getCurrentPeriodData() {
        return IndicatorDataContext.getCurrentPeriodData().orElse(Collections.emptyList());
    }

    /**
     * 并行执行历史数据查询
     * <p>
     * 根据配置决定是否查询去年全年数据，并将不同类型的历史数据分别存储
     * </p>
     *
     * @param context 上下文
     */
    private void executeHistoricalDataQuery(QueryContext context) {
        try {
            log.debug("开始并行获取历史数据, dataModelId: {}", context.getDataModelId());

            IndicatorQueryBuilder queryBuilder = new IndicatorQueryBuilder(
                context.getRequest(), context.getFieldGroups(), context.getDataConnection());

            StatisticPeriod currentPeriod = context.getRequest().getStatisticPeriod();

            // 并行获取历史数据
            CompletableFuture<List<Map<String, Object>>> lastPeriodFuture =
                CompletableFuture.supplyAsync(() -> getLastPeriodData(context, queryBuilder, currentPeriod),
                    indicatorQueryExecutor);

            CompletableFuture<List<Map<String, Object>>> lastYearFuture =
                CompletableFuture.supplyAsync(() -> getLastYearData(context, queryBuilder, currentPeriod),
                    indicatorQueryExecutor);

            // 根据配置决定是否并行获取去年整年数据
            CompletableFuture<List<Map<String, Object>>> lastYearFullFuture = null;
            boolean enableFullYearData = indicatorQueryProperties.isEnableLastYearFullData();

            if (enableFullYearData) {
                log.debug("配置启用去年整年数据获取功能, 开始并行查询去年全年数据, dataModelId: {}",
                    context.getDataModelId());
                lastYearFullFuture = CompletableFuture.supplyAsync(
                    () -> getLastYearFullData(context, queryBuilder, currentPeriod),
                    indicatorQueryExecutor);
            } else {
                log.debug("配置未启用去年整年数据获取功能, 跳过去年全年数据查询, dataModelId: {}",
                    context.getDataModelId());
            }

            // 等待所有历史数据查询完成
            if (lastYearFullFuture != null) {
                CompletableFuture.allOf(lastPeriodFuture, lastYearFuture, lastYearFullFuture).join();
            } else {
                CompletableFuture.allOf(lastPeriodFuture, lastYearFuture).join();
            }

            // 分别存储不同类型的历史数据
            List<Map<String, Object>> lastPeriodData = lastPeriodFuture.get();
            List<Map<String, Object>> lastYearData = lastYearFuture.get();

            // 添加到基础数据上下文（保持现有逻辑）
            IndicatorDataContext.addData(lastPeriodData);
            IndicatorDataContext.addData(lastYearData);

            log.debug("历史数据存储完成 - 上期数据: {}, 去年同期数据: {}, dataModelId: {}",
                lastPeriodData.size(), lastYearData.size(), context.getDataModelId());

            // 如果获取了去年整年数据，专门存储到去年全年数据上下文中
            if (lastYearFullFuture != null) {
                List<Map<String, Object>> lastYearFullData = lastYearFullFuture.get();

                // 专门存储去年全年数据，与去年同期数据分开管理
                IndicatorDataContext.setLastYearFullData(lastYearFullData);

                // 同时也添加到基础数据上下文中（保持兼容性）
                IndicatorDataContext.addData(lastYearFullData);

                log.info("去年整年数据获取并存储完成, 数据量: {}, dataModelId: {}",
                    lastYearFullData.size(), context.getDataModelId());
            }
            log.debug("历史数据查询和存储全部完成, dataModelId: {}", context.getDataModelId());
        } catch (Exception e) {
            log.error("获取历史数据失败, dataModelId: {}", context.getDataModelId(), e);
            throw IndicatorQueryException.dataQueryError("历史数据查询", e);
        }
    }

    /**
     * 获取上一周期数据
     *
     * @param context       上下文
     * @param queryBuilder  查询生成器
     * @param currentPeriod 当前期间
     * @return {@link List }<{@link Map }<{@link String }, {@link Object }>>
     */
    private List<Map<String, Object>> getLastPeriodData(QueryContext context,
        IndicatorQueryBuilder queryBuilder,
        StatisticPeriod currentPeriod) {
        try {
            StatisticPeriod lastPeriod = new StatisticPeriod(
                StatisticPeriod.minusPeriod(context.getPeriodConfig().getPeriodType(), 1L,
                    currentPeriod.getStartTime()),
                currentPeriod.getStartTime());

            return getDataByPeriod(context.getConnectionId(), context.getTableName(),
                lastPeriod, queryBuilder);
        } catch (Exception e) {
            log.warn("获取上一周期数据失败, 将返回空数据", e);
            return Collections.emptyList();
        }
    }

    /**
     * 获取去年同期数据
     *
     * @param context       上下文
     * @param queryBuilder  查询生成器
     * @param currentPeriod 当前期间
     * @return {@link List }<{@link Map }<{@link String }, {@link Object }>>
     */
    private List<Map<String, Object>> getLastYearData(QueryContext context,
        IndicatorQueryBuilder queryBuilder,
        StatisticPeriod currentPeriod) {
        try {
            StatisticPeriod lastYear = new StatisticPeriod(
                currentPeriod.getStartTime().minusYears(1),
                currentPeriod.getEndTime().minusYears(1));

            return getDataByPeriod(context.getConnectionId(), context.getTableName(),
                lastYear, queryBuilder);
        } catch (Exception e) {
            log.warn("获取去年同期数据失败, 将返回空数据", e);
            return Collections.emptyList();
        }
    }

    /**
     * 获取去年整年的完整数据
     * <p>
     * 基于年度周期配置，获取相对于当前统计周期的去年一整年的完整历史数据
     *
     * @param context       上下文
     * @param queryBuilder  查询生成器
     * @param currentPeriod 当前期间
     * @return {@link List }<{@link Map }<{@link String }, {@link Object }>>
     */
    private List<Map<String, Object>> getLastYearFullData(QueryContext context,
        IndicatorQueryBuilder queryBuilder,
        StatisticPeriod currentPeriod) {
        try {
            log.debug("开始获取去年整年数据, 当前周期: {} - {}",
                DateTimeUtils.formatStr(currentPeriod.getStartTime()),
                DateTimeUtils.formatStr(currentPeriod.getEndTime()));

            // 获取年度周期配置
            IndicatorPeriodConfigEntity yearPeriodEntity = indicatorPeriodConfigMapper.selectByPeriodType(
                IndicatorPeriodType.YEARLY.name());
            if (yearPeriodEntity == null) {
                log.warn("未找到年度周期配置，使用默认配置");
                yearPeriodEntity = new IndicatorPeriodConfigEntity(
                    IndicatorPeriodType.YEARLY,
                    IndicatorPeriodType.YEARLY.getDefaultConfig(),
                    null);
            }

            IndicatorPeriodConfig<?> yearConfig = yearPeriodEntity.getConfig();

            // 使用工具类和年度配置计算去年整年的时间范围
            StatisticPeriod lastYearFull = IndicatorPeriodConverter.calculateLastYearFullPeriod(
                currentPeriod, yearConfig);

            log.debug("去年整年时间范围: {} - {}",
                DateTimeUtils.formatStr(lastYearFull.getStartTime()),
                DateTimeUtils.formatStr(lastYearFull.getEndTime()));

            List<Map<String, Object>> result = getDataByPeriod(context.getConnectionId(),
                context.getTableName(), lastYearFull, queryBuilder);

            log.debug("去年整年数据获取成功, 数据量: {}", result.size());
            return result;

        } catch (Exception e) {
            log.warn("获取去年整年数据失败, 将返回空数据", e);
            return Collections.emptyList();
        }
    }

    /**
     * 根据周期获取数据
     *
     * @param connectionId 连接id
     * @param tableName    表名
     * @param period       时期
     * @param queryBuilder 查询生成器
     * @return {@link List }<{@link Map }<{@link String }, {@link Object }>>
     */
    private List<Map<String, Object>> getDataByPeriod(Integer connectionId, String tableName,
        StatisticPeriod period, IndicatorQueryBuilder queryBuilder) {
        try {
            String sql = queryBuilder.buildHistoricalQuerySql(tableName, period);
            return executeQuery(connectionId, sql);
        } catch (Exception e) {
            log.error("查询周期数据失败, 表名: {}, 周期: {} - {}", tableName,
                DateTimeUtils.formatStr(period.getStartTime()), DateTimeUtils.formatStr(period.getEndTime()), e);
            throw IndicatorQueryException.dataQueryError("周期数据查询", e);
        }
    }

    /**
     * 执行数据补齐
     * <p>
     * 根据配置的数据补齐策略，选择合适的补齐模式：
     * <ul>
     *   <li>去年同期补齐：根据当前数据周期确定去年对应的同期范围进行补齐</li>
     *   <li>去年全年补齐：基于去年全年数据进行补齐，补齐到年底</li>
     * </ul>
     * </p>
     *
     * @param currentData 当前数据
     * @param context     上下文
     * @return {@link List }<{@link Map }<{@link String }, {@link Object }>>
     */
    private List<Map<String, Object>> performDataCompletion(List<Map<String, Object>> currentData,
        QueryContext context) {
        try {
            log.debug("开始数据补齐处理, dataModelId: {}", context.getDataModelId());
            // 获取数据补齐策略
            DataCompletionStrategy strategy = getDataCompletionStrategy(context);
            // 策略空值检查：如果策略为 null，则不进行数据补齐处理
            if (strategy == null) {
                log.info("数据补齐策略为空，跳过数据补齐处理, dataModelId: {}", context.getDataModelId());
                return currentData;
            }
            log.debug("使用数据补齐策略: {}, dataModelId: {}", strategy.getDescription(), context.getDataModelId());
            // 验证全年补齐条件
            DataCompletionStrategy finalStrategy = validateAndAdjustStrategy(strategy, context);
            if (finalStrategy != strategy) {
                log.info("数据补齐策略已调整: {} -> {}, dataModelId: {}",
                    strategy.getDescription(), finalStrategy.getDescription(), context.getDataModelId());
            }
            // 根据最终策略获取相应的历史数据
            List<Map<String, Object>> lastPeriodData = getLastPeriodDataForStrategy(finalStrategy, context);
            List<Map<String, Object>> lastYearData = getLastYearDataForStrategy(finalStrategy, context);
            List<String> dimsFields = context.getStatisticDimensions();
            // 执行数据补齐
            List<Map<String, Object>> paddedData = dataCompletionService.completeData(
                currentData,
                lastPeriodData,
                lastYearData,
                context.getIndicatorConfig().getStatisticStrategyInfo().getPeriod(),
                context.getFieldGroups().startTimeField(),
                context.getFieldGroups().endTimeField(),
                dimsFields,
                finalStrategy  // 传递最终确定的补齐策略
            );
            if (CollectionUtils.isNotEmpty(paddedData)) {
                IndicatorDataContext.addCurrentPeriodData(paddedData);
                IndicatorDataContext.addData(paddedData);
                log.info("数据补齐成功, dataModelId: {}, 策略: {}, 补齐数据量: {}",
                    context.getDataModelId(), finalStrategy.getDescription(), paddedData.size());
            } else {
                log.debug("无需补齐数据, dataModelId: {}, 策略: {}",
                    context.getDataModelId(), finalStrategy.getDescription());
            }
            return currentData;
        } catch (Exception e) {
            log.error("数据补齐处理失败, dataModelId: {}", context.getDataModelId(), e);
            // 数据补齐失败时返回原始数据，不中断流程
            log.warn("数据补齐失败，使用原始数据继续处理, dataModelId: {}", context.getDataModelId());
            return currentData;
        }
    }

    /**
     * 验证并调整数据补齐策略
     * <p>
     * 只有当同时满足以下两个条件时才执行去年全年补齐：
     * <ul>
     *   <li>数据补齐策略为 FULL_YEAR_LAST_YEAR</li>
     *   <li>系统配置启用了去年整年数据获取功能 (isEnableLastYearFullData = true)</li>
     * </ul>
     * 如果上述条件不完全满足，则默认使用去年同期补齐策略
     * </p>
     *
     * @param strategy 原始策略
     * @param context  查询上下文
     * @return 验证后的最终策略
     */
    private DataCompletionStrategy validateAndAdjustStrategy(DataCompletionStrategy strategy, QueryContext context) {
        try {
            // 如果不是全年补齐策略，直接返回原策略
            if (!strategy.isFullYearLastYear()) {
                log.debug("策略验证通过: {}, dataModelId: {}",
                    strategy.getDescription(), context.getDataModelId());
                return strategy;
            }

            // 检查系统配置是否启用了去年整年数据获取功能
            boolean isFullDataEnabled = indicatorQueryProperties.isEnableLastYearFullData();

            if (isFullDataEnabled) {
                log.debug("全年补齐策略验证通过: 策略={}, 系统配置启用全年数据={}, dataModelId: {}",
                    strategy.getDescription(), isFullDataEnabled, context.getDataModelId());
                return strategy;
            } else {
                log.warn("全年补齐策略条件不满足，降级为去年同期补齐: 策略={}, 系统配置启用全年数据={}, dataModelId: {}",
                    strategy.getDescription(), isFullDataEnabled, context.getDataModelId());
                return DataCompletionStrategy.SAME_PERIOD_LAST_YEAR;
            }

        } catch (Exception e) {
            log.error("策略验证过程中发生异常, dataModelId: {}, 使用默认策略",
                context.getDataModelId(), e);
            return DataCompletionStrategy.SAME_PERIOD_LAST_YEAR;
        }
    }

    /**
     * 获取数据补齐策略
     * <p>
     * 从指标配置中获取数据补齐策略，支持以下情况的处理：
     * <ul>
     *   <li>如果配置中明确设置了策略，则返回配置的策略</li>
     *   <li>如果配置为空或获取失败，则返回默认的去年同期补齐策略</li>
     *   <li>如果配置中的策略为 null，则返回 null（表示不进行数据补齐）</li>
     * </ul>
     * </p>
     *
     * @param context 查询上下文
     * @return 数据补齐策略，可能为 null（表示不进行数据补齐）
     */
    private DataCompletionStrategy getDataCompletionStrategy(QueryContext context) {
        try {
            // 检查上下文和配置的完整性
            if (context == null) {
                log.warn("查询上下文为空，无法获取数据补齐策略");
                return null;
            }
            // 获取配置中的策略
            DataCompletionStrategy strategy = context.getIndicatorConfig()
                .getStatisticStrategyInfo()
                .getDataCompletionStrategy();
            if (strategy == null) {
                log.debug("配置中的数据补齐策略为空, dataModelId: {}", context.getDataModelId());
                return null;
            }
            log.debug("从配置中获取数据补齐策略: {}, dataModelId: {}",
                strategy.getDescription(), context.getDataModelId());
            return strategy;
        } catch (Exception e) {
            log.warn("获取数据补齐策略失败, 使用默认策略", e);
            return null;
        }
    }

    /**
     * 根据策略获取上一周期数据
     * <p>
     * 获取上一个统计周期的历史数据，用于数据补齐时的参考
     * </p>
     * <p>
     * 注意：上一周期数据的获取逻辑对于不同策略基本相同，主要用于补齐算法中的参考数据
     * </p>
     *
     * @param strategy 数据补齐策略
     * @param context  查询上下文
     * @return 上一周期数据
     */
    private List<Map<String, Object>> getLastPeriodDataForStrategy(DataCompletionStrategy strategy,
        QueryContext context) {
        try {
            if (strategy == null) {
                log.warn("数据补齐策略为空，无法获取上一周期数据, dataModelId: {}",
                    context != null ? context.getDataModelId() : "");
                return Collections.emptyList();
            }

            List<Map<String, Object>> lastPeriodData = IndicatorDataContext.getLastPeriodData()
                .orElse(Collections.emptyList());

            String dataModelId = context != null ? context.getDataModelId().toString() : "";
            log.debug("获取上一周期数据成功, 策略: {}, dataModelId: {}, 数据量: {}",
                strategy.getDescription(), dataModelId, lastPeriodData.size());
            return lastPeriodData;

        } catch (Exception e) {
            String dataModelId = context != null ? context.getDataModelId().toString() : "";
            log.warn("获取上一周期数据失败, 策略: {}, dataModelId: {}",
                strategy != null ? strategy.getDescription() : "null", dataModelId, e);
            return Collections.emptyList();
        }
    }

    /**
     * 根据策略获取去年数据
     * <p>
     * 明确区分去年同期数据与去年全年数据的获取逻辑：
     * <ul>
     *   <li>去年同期补齐策略：获取去年同期数据（与当前周期对应的历史数据）</li>
     *   <li>去年全年补齐策略：获取去年全年数据（需要系统配置支持）</li>
     * </ul>
     * </p>
     * <p>
     * <strong>重要说明</strong>：此方法调用前应已通过 validateAndAdjustStrategy 验证策略的有效性
     * </p>
     *
     * @param strategy 数据补齐策略（已验证）
     * @param context  查询上下文
     * @return 去年数据
     */
    private List<Map<String, Object>> getLastYearDataForStrategy(DataCompletionStrategy strategy,
        QueryContext context) {
        try {
            if (strategy == null) {
                log.warn("数据补齐策略为空，无法获取去年数据, dataModelId: {}",
                    context != null ? context.getDataModelId() : "");
                return Collections.emptyList();
            }

            List<Map<String, Object>> lastYearData;
            String dataModelId = context != null ? context.getDataModelId().toString() : "";

            if (strategy.isFullYearLastYear()) {
                // 全年补齐策略：获取去年全年数据
                lastYearData = IndicatorDataContext.getLastYearFullData().orElse(Collections.emptyList());
                log.debug("全年补齐策略获取去年全年数据, dataModelId: {}, 数据量: {}",
                    dataModelId, lastYearData.size());
            } else {
                // 去年同期补齐策略：获取去年同期数据
                lastYearData = IndicatorDataContext.getLastYearData().orElse(Collections.emptyList());
                log.debug("同期补齐策略获取去年同期数据, dataModelId: {}, 数据量: {}",
                    dataModelId, lastYearData.size());
            }
            return lastYearData;
        } catch (Exception e) {
            String dataModelId = context != null ? context.getDataModelId().toString() : "";
            log.error("获取去年数据失败, 策略: {}, dataModelId: {}",
                strategy != null ? strategy.getDescription() : "null", dataModelId, e);
            return Collections.emptyList();
        }
    }

    /**
     * 执行指标计算
     *
     * @param context 上下文
     * @return {@link Map }<{@link String }, {@link Map }<{@link String }, {@link Object }>>
     */
    private Map<String, Map<String, Object>> performCalculations(QueryContext context) {
        try {
            log.debug("开始执行指标计算, dataModelId: {}", context.getDataModelId());

            List<DataModelIndicatorField> applicationFields = context.getFieldGroups()
                .applicationFields().stream()
                .filter(DataModelIndicatorField::isEnable)
                .filter(e -> e.getConfig() instanceof IndicatorApplicationFieldConfig)
                .sorted(Comparator.comparing(DataModelIndicatorField::getExecuteOrder,
                    Comparator.nullsLast(Comparator.naturalOrder())))
                .toList();

            for (DataModelIndicatorField field : applicationFields) {
                executeFieldCalculation(field);
            }

            Map<String, Map<String, Object>> result = IndicatorDataContext.getAllCalculatedValues();
            log.debug("指标计算完成, 计算字段数量: {}", result.size());

            return result;

        } catch (Exception e) {
            log.error("指标计算失败, dataModelId: {}", context.getDataModelId(), e);
            throw IndicatorQueryException.calculationError("指标计算", e);
        }
    }

    /**
     * 执行单个字段的计算
     *
     * @param field 字段
     */
    private void executeFieldCalculation(DataModelIndicatorField field) {
        try {
            IndicatorApplicationFieldConfig config = (IndicatorApplicationFieldConfig) field.getConfig();
            String functionName = config.getFunction();

            if (StringUtils.isNotBlank(functionName)) {
                CalculationFunction calculateFunction = CalculationFunction.fromName(functionName);
                if (calculateFunction != null) {
                    Map<String, Object> calculatedValues = calculateFunction.apply(config);
                    if (calculatedValues != null && !calculatedValues.isEmpty()) {
                        IndicatorDataContext.addCalculatedValue(field.getEnName(), calculatedValues);
                    }
                } else {
                    log.warn("没有找到函数 {} 对应的计算函数", functionName);
                }
            }
        } catch (Exception e) {
            log.error("字段计算失败, fieldName: {}", field.getEnName(), e);
            throw IndicatorQueryException.calculationError(field.getEnName(), e);
        }
    }

    /**
     * 合并计算结果到数据中
     *
     * @param resultData          结果数据
     * @param allCalculatedValues 所有计算值
     */
    private void mergeCalculationResults(List<Map<String, Object>> resultData,
        Map<String, Map<String, Object>> allCalculatedValues) {
        if (allCalculatedValues.isEmpty()) {
            return;
        }

        try {
            log.debug("开始合并计算结果, 计算字段数量: {}", allCalculatedValues.size());

            Function<Map<String, Object>, String> keyGenerator = IndicatorDataContext.getKeyGenerator()
                .orElseThrow(() -> new IllegalStateException("键生成器未初始化"));

            for (Map<String, Object> row : resultData) {
                String key = keyGenerator.apply(row);
                allCalculatedValues.forEach((fieldName, calculatedValues) -> {
                    if (calculatedValues.containsKey(key)) {
                        row.put(fieldName, calculatedValues.get(key));
                    }
                });
            }

            log.debug("计算结果合并完成");

        } catch (Exception e) {
            log.error("合并计算结果失败", e);
            throw IndicatorQueryException.dataProcessingError("合并计算结果", e);
        }
    }

    /**
     * 应用排序
     * <p>
     * 排序策略：
     * 1. 固定排序（FIXED）：无论字段类型如何，都必须在内存中进行排序，因为数据库无法处理固定顺序排序
     * 2. 应用字段排序：计算得出的字段必须在内存中排序，因为数据库中不存在这些字段
     * 3. 元数据字段的ASC/DESC排序：优先使用数据库排序（性能更好）
     *
     * @param data    数据
     * @param context 上下文
     * @return {@link List }<{@link Map }<{@link String }, {@link Object }>>
     */
    private List<Map<String, Object>> applySorting(List<Map<String, Object>> data, QueryContext context) {
        IndicatorSortField sortField = context.getRequest().getSortField();
        if (sortField == null) {
            return data;
        }

        try {
            log.debug("开始应用排序, 排序字段: {}, 排序类型: {}",
                sortField.getField(), sortField.getOrder());

            // 判断是否需要内存排序
            boolean needsMemorySorting = shouldUseMemorySorting(sortField, context);

            if (needsMemorySorting) {
                List<Map<String, Object>> sortedData = IndicatorDataSorter.sort(data, sortField);
                log.debug("内存排序完成, 排序字段: {}, 排序类型: {}, 数据量: {}",
                    sortField.getField(), sortField.getOrder(), sortedData.size());
                return sortedData;
            }

            // 使用数据库排序结果
            log.debug("使用数据库排序结果, 排序字段: {}, 排序类型: {}",
                sortField.getField(), sortField.getOrder());
            return data;

        } catch (Exception e) {
            log.error("应用排序失败, 排序字段: {}, 排序类型: {}",
                sortField.getField(), sortField.getOrder(), e);
            throw IndicatorQueryException.dataProcessingError("应用排序", e);
        }
    }

    /**
     * 判断是否需要使用内存排序
     * <p>
     * 内存排序的条件：
     * 1. 固定排序（FIXED）：数据库无法处理固定顺序，必须内存排序
     * 2. 应用字段排序：计算字段在数据库中不存在，必须内存排序
     *
     * @param sortField 排序字段配置
     * @param context   查询上下文
     * @return true表示需要内存排序，false表示可以使用数据库排序
     */
    private boolean shouldUseMemorySorting(IndicatorSortField sortField, QueryContext context) {
        // 1. 固定排序必须在内存中进行
        if (sortField.getOrder() == IndicatorSortOrder.FIXED) {
            log.debug("固定排序必须使用内存排序, 排序字段: {}", sortField.getField());
            return true;
        }

        // 2. 应用字段（计算字段）必须在内存中排序
        List<String> applicationFields = context.getFieldGroups().getApplicationFieldNames();
        if (applicationFields.contains(sortField.getField())) {
            log.debug("应用字段必须使用内存排序, 排序字段: {}", sortField.getField());
            return true;
        }

        // 3. 元数据字段的ASC/DESC排序可以使用数据库排序
        log.debug("元数据字段的ASC/DESC排序使用数据库排序, 排序字段: {}", sortField.getField());
        return false;
    }

    /**
     * 按目标周期筛选结果
     *
     * @param data    数据
     * @param context 上下文
     * @return {@link List }<{@link Map }<{@link String }, {@link Object }>>
     */
    private List<Map<String, Object>> filterByTargetPeriods(List<Map<String, Object>> data, QueryContext context) {
        List<StatisticPeriod> targetPeriods = context.getRequest().getTargetPeriods();
        String startTimeKey = context.getFieldGroups().startTimeField();

        if (CollectionUtils.isEmpty(targetPeriods) || StringUtils.isBlank(startTimeKey)) {
            return data;
        }

        try {
            log.debug("开始按目标周期筛选, 目标周期数量: {}", targetPeriods.size());

            List<Map<String, Object>> filteredData = data.stream().filter(item -> {
                Object timeValue = item.get(startTimeKey);
                if (timeValue == null) {
                    return false;
                }
                try {
                    LocalDateTime itemStartTime;
                    if (timeValue instanceof LocalDateTime time) {
                        itemStartTime = time;
                    } else {
                        itemStartTime = DateTimeUtils.parse(timeValue.toString());
                    }
                    return targetPeriods.stream().anyMatch(
                        period -> !itemStartTime.isBefore(period.getStartTime())
                            && itemStartTime.isBefore(period.getEndTime()));
                } catch (Exception e) {
                    log.warn("无法解析时间戳: {}", timeValue, e);
                    return false;
                }
            }).collect(Collectors.toCollection(ArrayList::new));

            log.debug("目标周期筛选完成, 筛选后数据量: {}", filteredData.size());
            return filteredData;

        } catch (Exception e) {
            log.error("按目标周期筛选失败", e);
            throw IndicatorQueryException.dataProcessingError("目标周期筛选", e);
        }
    }

    /**
     * 过滤返回字段
     *
     * @param data         数据
     * @param returnFields 返回字段
     * @return {@link List }<{@link Map }<{@link String }, {@link Object }>>
     */
    private List<Map<String, Object>> filterReturnFields(List<Map<String, Object>> data, List<String> returnFields) {
        if (returnFields == null || returnFields.isEmpty()) {
            return data;
        }

        try {
            log.debug("开始过滤返回字段, 返回字段数量: {}", returnFields.size());

            List<Map<String, Object>> filteredData = data.stream().map(row -> {
                Map<String, Object> filteredRow = new HashMap<>();
                returnFields.forEach(field -> {
                    if (row.containsKey(field)) {
                        filteredRow.put(field, row.get(field));
                    }
                });
                return filteredRow;
            }).toList();

            log.debug("返回字段过滤完成");
            return filteredData;

        } catch (Exception e) {
            log.error("过滤返回字段失败", e);
            throw IndicatorQueryException.dataProcessingError("过滤返回字段", e);
        }
    }
}
